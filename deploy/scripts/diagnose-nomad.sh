#!/bin/bash

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

JOB_NAME="${1:-traefik}"

echo -e "${GREEN}诊断 Nomad 作业: $JOB_NAME${NC}"
echo "========================================"

# 检查 Nomad 连接
echo -e "${BLUE}1. 检查 Nomad 连接...${NC}"
if ! nomad status >/dev/null 2>&1; then
    echo -e "${RED}错误: 无法连接到 Nomad${NC}"
    echo "请检查 NOMAD_ADDR 环境变量或 Nomad 服务状态"
    exit 1
fi
echo -e "${GREEN}✓ Nomad 连接正常${NC}"

# 检查作业状态
echo -e "${BLUE}2. 检查作业状态...${NC}"
if ! nomad job status "$JOB_NAME" >/dev/null 2>&1; then
    echo -e "${RED}错误: 作业 $JOB_NAME 不存在${NC}"
    echo "可用的作业:"
    nomad job status
    exit 1
fi

echo "作业状态:"
nomad job status "$JOB_NAME"

# 检查分配状态
echo -e "${BLUE}3. 检查分配状态...${NC}"
ALLOC_ID=$(nomad job status "$JOB_NAME" | grep -E "pending|running|failed" | head -1 | awk '{print $1}')

if [[ -n "$ALLOC_ID" ]]; then
    echo "分配 ID: $ALLOC_ID"
    echo "分配详情:"
    nomad alloc status "$ALLOC_ID"
    
    echo -e "${BLUE}4. 检查分配事件...${NC}"
    nomad alloc status -verbose "$ALLOC_ID" | grep -A 20 "Recent Events"
    
    echo -e "${BLUE}5. 检查资源使用...${NC}"
    nomad alloc status "$ALLOC_ID" | grep -A 10 "Allocated Resources"
else
    echo -e "${YELLOW}警告: 未找到分配${NC}"
fi

# 检查节点状态
echo -e "${BLUE}6. 检查节点状态...${NC}"
echo "可用节点:"
nomad node status

# 检查节点详情
echo -e "${BLUE}7. 检查节点资源...${NC}"
nomad node status | tail -n +2 | while read line; do
    NODE_ID=$(echo "$line" | awk '{print $1}')
    NODE_NAME=$(echo "$line" | awk '{print $2}')
    echo "节点 $NODE_NAME ($NODE_ID):"
    nomad node status "$NODE_ID" | grep -E "CPU|Memory|Disk"
    echo "---"
done

# 检查端口占用
echo -e "${BLUE}8. 检查端口占用...${NC}"
PORTS=(80 443 8080)
for port in "${PORTS[@]}"; do
    echo -n "端口 $port: "
    if ss -tlnp | grep ":$port " >/dev/null 2>&1; then
        echo -e "${RED}已占用${NC}"
        ss -tlnp | grep ":$port "
    else
        echo -e "${GREEN}可用${NC}"
    fi
done

# 检查 Docker
echo -e "${BLUE}9. 检查 Docker 状态...${NC}"
if command -v docker >/dev/null 2>&1; then
    if docker info >/dev/null 2>&1; then
        echo -e "${GREEN}✓ Docker 运行正常${NC}"
        echo "Docker 镜像:"
        docker images | grep traefik || echo "未找到 Traefik 镜像"
    else
        echo -e "${RED}✗ Docker 服务异常${NC}"
    fi
else
    echo -e "${YELLOW}! Docker 未安装${NC}"
fi

# 检查数据目录
echo -e "${BLUE}10. 检查数据目录...${NC}"
DATA_DIRS=("/opt/data/traefik" "/opt/data/postgres" "/opt/data/registry" "/opt/data/app")
for dir in "${DATA_DIRS[@]}"; do
    if [[ -d "$dir" ]]; then
        echo -e "${GREEN}✓ $dir 存在${NC}"
        ls -la "$dir" | head -3
    else
        echo -e "${RED}✗ $dir 不存在${NC}"
    fi
done

echo -e "${GREEN}诊断完成${NC}"
echo "========================================"

# 提供建议
echo -e "${YELLOW}常见解决方案:${NC}"
echo "1. 如果端口被占用，停止占用进程或修改配置使用其他端口"
echo "2. 如果资源不足，增加节点资源或降低作业资源要求"
echo "3. 如果节点约束问题，检查节点标签和作业约束"
echo "4. 如果 Docker 问题，重启 Docker 服务"
echo "5. 如果数据目录问题，创建缺失的目录"
