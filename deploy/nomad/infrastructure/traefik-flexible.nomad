job "traefik-flexible" {
  datacenters = ["dc1"]
  type = "service"

  group "traefik" {
    count = 1

    # 使用动态端口，避免冲突
    network {
      port "http" {}
      port "https" {}
      port "api" {}
    }

    service {
      name = "traefik"
      port = "http"

      tags = [
        "traefik",
        "load-balancer"
      ]

      check {
        name     = "traefik-health"
        type     = "tcp"
        port     = "http"
        interval = "10s"
        timeout  = "3s"
      }
    }

    task "traefik" {
      driver = "docker"

      config {
        image = "traefik:v3.0"
        ports = ["http", "https", "api"]
        force_pull = false

        args = [
          "--api.dashboard=true",
          "--api.insecure=true",
          "--providers.consul.endpoints=${CONSUL_ADDR}",
          "--providers.consul.watch=true",
          "--entrypoints.web.address=:${NOMAD_PORT_http}",
          "--entrypoints.websecure.address=:${NOMAD_PORT_https}",
          "--entrypoints.traefik.address=:${NOMAD_PORT_api}",
          "--log.level=INFO"
        ]
      }

      resources {
        cpu    = 200
        memory = 256
      }

      env {
        CONSUL_ADDR = "${CONSUL_ADDR}"
      }
    }
  }
}
