job "traefik-debug" {
  datacenters = ["dc1"]
  type = "service"

  # 移除所有约束，允许在任何节点运行
  group "traefik" {
    count = 1

    # 使用动态端口避免冲突
    network {
      port "http" {
        to = 80
      }
      port "https" {
        to = 443
      }
      port "api" {
        to = 8080
      }
    }

    service {
      name = "traefik-debug"
      port = "http"

      check {
        name     = "traefik-health"
        type     = "tcp"
        port     = "http"
        interval = "10s"
        timeout  = "3s"
      }
    }

    task "traefik" {
      driver = "docker"

      config {
        image = "traefik:v3.0"
        ports = ["http", "https", "api"]
        force_pull = false

        args = [
          "--api.dashboard=true",
          "--api.insecure=true",
          "--providers.consul.endpoints=consul.service.consul:8500",
          "--providers.consul.watch=true",
          "--entrypoints.web.address=:80",
          "--entrypoints.websecure.address=:443",
          "--entrypoints.traefik.address=:8080",
          "--log.level=DEBUG",
          "--accesslog=true"
        ]
      }

      resources {
        cpu    = 100  # 降低资源要求
        memory = 128
      }

      # 添加环境变量用于调试
      env {
        TRAEFIK_LOG_LEVEL = "DEBUG"
      }
    }
  }
}
