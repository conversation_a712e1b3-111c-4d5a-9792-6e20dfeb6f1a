# 监控环境配置

# Grafana 配置
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=your_secure_password_here

# 域名配置
DOMAIN_NAME=your-domain.com

# Nomad 配置
NOMAD_ADDR=http://localhost:4646
CONSUL_ADDR=http://localhost:8500

# 日志配置
LOG_LEVEL=info
LOG_OUTPUT=both
LOG_FILE_PATH=/opt/data/app/logs/app.log
LOG_MAX_SIZE=100
LOG_MAX_BACKUPS=5
LOG_MAX_AGE=30
LOG_COMPRESS=true

# 监控开关
METRICS_ENABLED=true
METRICS_PORT=9090
METRICS_PATH=/metrics

LOKI_ENABLED=true
LOKI_ENDPOINT=http://loki:3100

TRACING_ENABLED=false
JAEGER_ENDPOINT=http://jaeger:14268/api/traces
