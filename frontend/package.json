{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@ant-design/pro-components": "^2.8.6", "@ant-design/pro-table": "^3.18.6", "@emotion/react": "^11.14.0", "@tanstack/react-query": "^5.66.9", "antd": "^5.24.2", "axios": "^1.8.1", "dayjs": "^1.11.13", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.2.0", "zustand": "^5.0.3", "@ant-design/colors": "^7.1.0", "framer-motion": "^11.15.0", "lucide-react": "^0.468.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/node": "^22.13.5", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "10.4.16", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "postcss": "8.4.33", "sass": "^1.85.1", "tailwindcss": "3.4.1", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}, "packageManager": "pnpm@10.6.2"}